<script setup lang="ts">
import type WaistTrend from '~/components/user/checkin/waist-trend.vue'

const dayjs = useDayjs()

type TabType = '周' | '月' | '年'

interface ChartData {
    labels: string[]
    data: number[]
}
interface WaistChartData {
    周: ChartData
    月: ChartData
    年: ChartData
}

const waistTrendChartRef = ref<InstanceType<typeof WaistTrend>>()
const waistChartData = reactive<WaistChartData>({
    周: { labels: [], data: [] },
    月: { labels: [], data: [] },
    年: { labels: [], data: [] },
})
const someWaistCircumferenceData = ref<any[]>([])
const showWaistSheet = ref(false)

const allWaistData = ref<Array<{ checkInDate: string, waistCircumference: number }>>([])
const isWaistDataLoaded = ref(false)
const isLoadingWaistData = ref(false)

const timeRangeConfig = {
    周: { days: 7, label: 'MM月DD日' },
    月: { days: 30, label: 'MM月DD日' },
    年: { days: 365, label: 'MM月DD日' },
} as const

const { data: waistData, refresh } = useAPI<{
    waistCircumference: number
    waistCircumferenceTrend: number
}>('/checkInCustomerWaistCircumference/get', {
    method: 'post',
    body: { kind: '6', checkInDate: '' },
})
const { data: archiveResults, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')

// 一次性获取所有腰围数据
async function fetchAllWaistData() {
    if (isWaistDataLoaded.value || isLoadingWaistData.value) return
    isLoadingWaistData.value = true

    try {
        const res = await useWrapFetch<BaseResponse<any>>('/api/checkInCustomerWaistCircumference/getWaistCircumferenceTrendByTypeWithTrend', {
            method: 'get',
            params: { type: 'all' },
        })

        if (Array.isArray(res.results?.waistCircumferenceList)) {
            // 按时间排序，确保数据一致性
            allWaistData.value = res.results.waistCircumferenceList.sort((a: any, b: any) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )
            // 更新列表显示数据（最新5条）
            someWaistCircumferenceData.value = [...allWaistData.value]
                .sort((a, b) => dayjs(b.checkInDate).diff(dayjs(a.checkInDate)))
                .slice(0, 5)
            isWaistDataLoaded.value = true
        } else {
            allWaistData.value = []
            someWaistCircumferenceData.value = []
            isWaistDataLoaded.value = true
        }
    } catch (error) {
        console.error('获取腰围数据失败:', error)
        allWaistData.value = []
        someWaistCircumferenceData.value = []
        isWaistDataLoaded.value = false
    } finally {
        isLoadingWaistData.value = false
    }
}

// 按时间范围处理腰围数据
function processWaistDataByTimeRange(allData: Array<{ checkInDate: string, waistCircumference: number }>, tab: TabType): ChartData {
    const config = timeRangeConfig[tab]
    const cutoffDate = dayjs().subtract(config.days, 'day')
    const filteredData = allData
        .filter(item => dayjs(item.checkInDate).isAfter(cutoffDate))
        .sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))

    const labels = filteredData.map(item => dayjs(item.checkInDate).format(config.label))
    const data = filteredData.map(item => item.waistCircumference)

    return { labels, data }
}

// 优化后的tab切换处理 - 基于已加载的数据进行前端筛选
async function handleTabChange(tab: TabType) {
    try {
        if (!tab) return

        // 确保数据已加载
        if (!isWaistDataLoaded.value) {
            await fetchAllWaistData()
        }

        // 基于全量数据进行时间范围筛选
        if (allWaistData.value.length > 0) {
            waistChartData[tab] = processWaistDataByTimeRange(allWaistData.value, tab)
        }
    } catch (error) {
        console.error('处理腰围趋势数据失败:', error)
    }
}

async function refreshAllWaistData() {
    allWaistData.value = []
    isWaistDataLoaded.value = false
    await fetchAllWaistData()
    initializeWaistChartData()
}

function initializeWaistChartData() {
    const timeRanges = ['周', '月', '年'] as const
    timeRanges.forEach((timeRange) => {
        if (allWaistData.value.length > 0) {
            waistChartData[timeRange] = processWaistDataByTimeRange(allWaistData.value, timeRange)
        }
    })
}

onMounted(async () => {
    await fetchAllWaistData()
    handleTabChange('周')
})

function handleWeightSuccess() {
    showWaistSheet.value = false
    refresh()
    refreshAllWaistData()
}
</script>

<template>
    <div class="p-16px h-full flex flex-col gap-12px">
        <user-checkin-waist-trend
            ref="waistTrendChartRef"
            :chart-data="waistChartData"
            :is-visible="true"
            @tab-change="handleTabChange"
        >
            <template #title>
                <div class="flex items-center gap-8px">
                    <div class="text-t-5 text-14px font-500">
                        腰围趋势
                    </div>
                    <div class="text-11px flex items-center gap-2px text-t-3">
                        变化值
                        <div
                            :class="(waistData?.results?.waistCircumferenceTrend || 0) > 0 ? 'text-#F98804' : (waistData?.results?.waistCircumferenceTrend || 0) < 0 ? 'text-#00AC97' : 'text-t-3'"
                        >
                            {{ (waistData?.results?.waistCircumferenceTrend || 0) > 0 ? '↑' : (waistData?.results?.waistCircumferenceTrend || 0) < 0 ? '↓' : '-' }}
                            {{ (waistData?.results?.waistCircumferenceTrend || 0) !== 0 ? Math.abs((waistData?.results?.waistCircumferenceTrend || 0)).toFixed(2) : '' }}
                        </div>
                    </div>
                </div>
            </template>
            <template #empty-state>
                <div class="i-custom:checkin-waist w-32px h-32px"></div>
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="showWaistSheet = true"
                >
                    去记录
                </div>
            </template>
        </user-checkin-waist-trend>

        <div v-if="someWaistCircumferenceData.length" class="bg-white rd-10px p-16px flex flex-col gap-12px w-full">
            <div class="flex items-end gap-8px text-t-5 text-14px font-600">
                腰围记录
            </div>
            <div class="flex flex-col gap-6px">
                <div v-for="item in someWaistCircumferenceData" :key="item.checkInDate" class="flex items-center justify-between gap-8px">
                    <div class="text-t-4 text-13px">{{ dayjs(item.checkInDate).format('MM月DD日') }}</div>
                    <div class="flex items-center gap-8px text-t-5 text-18px font-600">
                        {{ item.waistCircumference }}cm
                    </div>
                </div>
            </div>
            <div
                v-if="someWaistCircumferenceData.length > 5"
                class="flex items-center justify-center text-t-3 text-13px cursor-pointer"
                @click="navigateTo('/user/checkin/waist-records')"
            >
                查看更多 >
            </div>
        </div>
        <van-empty v-else description="暂无腰围数据" />
    </div>
    <div class="flex fixed bottom-36px left-0 right-0 justify-center">
        <van-button round type="primary" class="w-223px! h-50px!" @click="showWaistSheet = true">记录今日腰围</van-button>
    </div>
    <user-checkin-waist
        v-model="showWaistSheet" :waist="Number(archiveResults?.results?.waist)"
        @success="handleWeightSuccess"
    />
</template>

<style lang="scss">
.box {
    &:before {
        background-color: #00B42A;
    }
}
</style>
